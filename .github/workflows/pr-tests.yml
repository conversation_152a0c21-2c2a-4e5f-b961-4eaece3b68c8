name: PR Tests

on:
  pull_request:
    branches:
      - main

jobs:
  test:
    name: Run Tests
    runs-on: ubuntu-latest
    steps:
      - name: 🏗️ Checkout code
        uses: actions/checkout@v4

      - name: 🏗️ Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 22.14.0
          cache: npm
          cache-dependency-path: |
            package-lock.json
            functions/package-lock.json
            functions/report-generation/package-lock.json
            functions/swagger-docs/package-lock.json
            web-sign-up/package-lock.json

      - name: 📦 Install dependencies (all modules)
        run: npm run ci:all

      - name: 🧪 Run tests
        run: npm run test:ci
