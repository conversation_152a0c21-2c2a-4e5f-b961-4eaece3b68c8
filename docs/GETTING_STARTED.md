# Open Terminal app on your Mac

cd ~                    # Go to home directory
mkdir git              # Create git directory
cd git                 # Enter git directory

git clone https://github.com/FlyBodies/fly-fit
cd fly-fit

brew bundle

nvm install 22         # Install Node.js version 22
nvm use 22             # Switch to Node.js 22
node -v                # Verify: should show 22.x.x

code fly-fit           # Opens project in VSCode

npm install            # Install all project dependencies (~5-10 minutes)

npm run setup          # Follow all prompts carefully

# Re-run individual setup commands if needed:
npm run setup:firebase    # If Firebase auth failed
npm run setup:eas         # If Expo auth failed
npm run setup:dotenv      # If Dotenv auth failed

sudo xcode-select -s /Applications/Xcode.app/Contents/Developer

npm run ios:dev        # Builds native app + starts dev server (~10-15 minutes)

npm run android:dev    # Builds native app + starts dev server (~10-15 minutes)

› Press a │ open Android
› Press i │ open iOS simulator
› Press w │ open web
› Press r │ reload app
› Press m │ toggle menu
› Press ? │ show all commands

npm start  # Start dev server only (uses existing native build)

# Follow the detailed steps in our Deployment Guide

npm run serve          # Starts local Firebase emulators

nvm use 22             # Ensure you're using Node 22
node -v                # Verify version

# Ensure Xcode command line tools are set correctly
sudo xcode-select -s /Applications/Xcode.app/Contents/Developer

npm run clean          # Clean project
npm install            # Reinstall
npm run ios:dev        # Rebuild for correct environment