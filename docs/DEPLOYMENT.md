# Deployment Guide

This guide covers the complete deployment process for FlyFit, including mobile apps, backend services, and web applications. **FlyFit uses a local deployment script** - no automated CI/CD pipelines at this time, although it can easily be setup and configured at some point in the future.

> [!IMPORTANT]
> **Prerequisites Required**: Complete the [Getting Started Guide](./GETTING_STARTED.md) and ensure you have all required access permissions before attempting deployments.

## Overview

### What Gets Deployed

FlyFit consists of **multiple deployable components**:

1. **📱 Mobile Apps** - iOS and Android apps via EAS (Expo Application Services)
2. **⚡ Backend Services** - Firebase Functions, Firestore rules, and hosting
3. **🌐 Web Applications** - Sign-up portal and link redirect pages
4. **📊 Static Content** - Wellness content and app configuration

### Deployment Strategy

**Manual Local Process:**
- ✅ **Developer-controlled** - You run deployment scripts locally
- ✅ **Environment selection** - Deploy to dev or production as needed
- ✅ **Quality gates** - Automated testing and validation before deployment
- ✅ **Automated Version Releases** - Automatic versioning and release management

**Two Environments:**
- **Development** (`fly-fit-dev`) - Testing and development
- **Production** (`fly-fit`) - Live user environment

> [!NOTE]
> **No staging environment**: We use a simple dev → prod deployment model for faster iteration, and features are meant to be locked behind feature flags if they are not ready for releasing but are in the main branch.

## Quick Reference

**Common Deployment Commands:**
```bash
npm run ci-cd:local              # Full deployment workflow (recommended)
npm run ci-cd:local:update       # JavaScript-only updates (EAS Update)
npm run ci-cd:local:rollback     # Rollback EAS updates

# Manual backend deployments
npm run deploy:local:dev         # Deploy all backend to dev
npm run deploy:local:prod        # Deploy all backend to prod
```

**Key Resources:**
- [EAS Build Dashboard](https://expo.dev/accounts/fly-bodies/projects/fly-fit/builds) - Monitor app builds
- [Firebase Console (Prod)](https://console.firebase.google.com/u/0/project/fly-fit/firestore) - Production backend
- [Firebase Console (Dev)](https://console.firebase.google.com/u/0/project/fly-fit-dev/firestore) - Development backend
- [App Store Connect](https://appstoreconnect.apple.com/apps/**********/appstore/ios/version/inflight) - iOS app management
- [Google Play Console](https://play.google.com/console/u/0/developers/5892814659496571933/app/4973741014673037397/app-dashboard) - Android app management

## Deployment Architecture

### Environment Setup

```mermaid
graph TD
    subgraph "Development Environment"
        DD[Dev Database<br/>fly-fit-dev]
        DF[Dev Functions<br/>fly-fit-dev]
        DH[Dev Hosting<br/>fly-fit-dev]
    end

    subgraph "Production Environment"
        PD[Prod Database<br/>fly-fit]
        PF[Prod Functions<br/>fly-fit]
        PH[Prod Hosting<br/>fly-fit]
    end

    subgraph "Mobile Applications"
        DA[Dev App<br/>com.flybodies.flyfitdev]
        PA[Prod App<br/>com.flybodies.flyfit]
    end

    subgraph "Build & Deploy Tools"
        LS[Local CI/CD Script<br/>npm run ci-cd:local]
        EAS[Expo Application Services]
    end

    DA --> DD
    DA --> DF

    PA --> PD
    PA --> PF

    LS --> DF
    LS --> PF
    LS --> DH
    LS --> PH
    LS --> EAS

    EAS --> DA
    EAS --> PA
```

### Deployment Process Flow

**Manual Deployment Workflow:**
```mermaid
sequenceDiagram
    participant D as Developer
    participant S as CI/CD Script
    participant F as Firebase
    participant E as EAS Build
    participant A as App Stores
    participant U as Users

    Note over D,U: Manual Local Deployment Process

    D->>S: npm run ci-cd:local
    S->>S: Run tests & linting
    S->>S: Prompt environment selection

    alt Backend Deployment
        S->>F: Deploy functions to selected env
        S->>F: Deploy hosting to selected env
        F->>U: Backend live immediately
    end

    alt Mobile App Deployment
        S->>E: Trigger EAS build
        E->>E: Build iOS/Android

        alt Development Build
            E->>D: Internal distribution
        end

        alt Production Build
            E->>A: Submit to app stores
            A->>A: Review process
            A->>U: Release to users
        end
    end
```

## Prerequisites

> [!WARNING]
> **Complete setup required**: You must have a fully configured development environment before deploying. Missing prerequisites will cause deployment failures.

### Required Access & Authentication

**Service Account Access:**
- ✅ **[EAS/Expo Account](https://expo.dev/accounts/fly-bodies/projects/fly-fit/builds)** - Mobile app builds
- ✅ **[Firebase Projects](https://console.firebase.google.com/u/0/project/fly-fit/firestore)** - Backend deployment
- ✅ **[Dotenv Vault](https://vault.dotenv.org/ui/ui1/project/RoC69x/environment/4mCPM3p)** - Environment variables
- ✅ **[GitHub Access](https://github.com/FlyBodies/fly-fit)** - Repository and release management

> [!NOTE]
> **Need access?** Contact the team lead to be added to all required organizations and projects.

### Validation Checklist

**Verify your authentication:**
```bash
# Check all required authentications
eas whoami                  # Should show your Expo account
firebase login              # Should show you're logged in
gh auth status              # Should show GitHub authentication
npm run env:local:prod      # Should load production environment variables
```

**Required files in project root:**
- [ ] `gh_token` - GitHub personal access token file
- [ ] `data/config/gcloud-dev.json` - Development service account key
- [ ] `data/config/gcloud-prod.json` - Production service account key

### Setting Up Service Account Keys

**Google Cloud service account keys** are required for deployment authentication:

1. **Navigate to Google Cloud Console:**
    - [Development Project](https://console.cloud.google.com/iam-admin/serviceaccounts/details/113958155132762017984/keys?inv=1&invt=Abyngw&walkthrough_id=iam--create-service-account-keys&project=fly-fit-dev&supportedpurview=project)
    - [Production Project](https://console.cloud.google.com/iam-admin/serviceaccounts/details/106571782229660349714/keys?inv=1&invt=Abyngw&walkthrough_id=iam--create-service-account-keys&project=fly-fit&supportedpurview=project)

2. **Create new service account key:**
    - Click `+ ADD KEY` → `Create new key`
    - Select `JSON` as the key type
    - Click `Create` to download

3. **Install the key files:**
    ```bash
    # Move downloaded files to correct location
    mv ~/Downloads/fly-fit-*.json data/config/

    # Rename files appropriately
    mv data/config/fly-fit-dev-*.json data/config/gcloud-dev.json
    mv data/config/fly-fit-*.json data/config/gcloud-prod.json
    ```

### Setting Up GitHub Token

**GitHub personal access token** is required for release management:

```bash
# Authenticate with GitHub CLI
gh auth login

# Verify authentication
gh auth status

# Create token file for deployment scripts
gh auth token > gh_token
```

## Full Deployment Process

### Primary Deployment Command

**The main deployment workflow** handles all aspects of deployment:

```bash
npm run ci-cd:local
```

**What this script does:**
1. ✅ **Quality checks** - Runs tests, type checking, and linting
2. ✅ **Environment selection** - Prompts for dev/prod deployment
3. ✅ **Backend deployment** - Deploys Firebase Functions and hosting
4. ✅ **Mobile app builds** - Creates iOS/Android builds via EAS
5. ✅ **App store submission** - Optionally submits to app stores

> [!TIP]
> **Recommended approach**: Use `npm run ci-cd:local` for all deployments. It provides the most comprehensive and safe deployment process.

### Interactive Deployment Options

**When you run the CI/CD script**, you'll be prompted with these options:

| Prompt | Recommended | Notes |
|--------|-------------|-------|
| **Run tests, typecheck, and linter?** | ✅ Yes | Always run quality checks to catch bugs |
| **Build for DEV environment?** | ❌ Usually No | Most deployments are to production |
| **Build for PROD environment?** | ✅ Yes | Primary deployment target |
| **Use EAS for builds?** | ✅ Yes | 💸 Costs $2/iOS + $1/Android but provides reliable builds |
| **Deploy, release, and submit builds?** | ❌ Usually No | Only for production app store releases |
| **Build for iOS?** | ✅ Yes | Include unless iOS-specific issue |
| **Build for Android?** | ✅ Yes | Include unless Android-specific issue |

> [!NOTE]
> **Cost consideration**: EAS builds cost money but provide reliable, consistent builds. Local builds are free but may have environment-specific issues.

## JavaScript-Only Updates (EAS Update)

### When to Use EAS Updates

**EAS Updates** allow you to push JavaScript changes without app store approval:

✅ **Use EAS Updates for:**
- Bug fixes in JavaScript/TypeScript code
- UI/UX improvements
- Content updates
- Small feature tweaks

❌ **Do NOT use EAS Updates for:**
- Native dependency changes
- Major new features (require app store approval)
- Breaking changes
- Expo SDK upgrades

### Deploying EAS Updates

**Quick JavaScript-only deployment:**
```bash
npm run ci-cd:local:update
```

**What this does:**
- Bundles JavaScript changes
- Pushes update to existing app installations
- Users get updates on next app launch
- No app store review required

> [!WARNING]
> **JavaScript-only limitation**: EAS Updates only work for JavaScript changes. Any native code changes require a full app build and store submission.

### Rolling Back EAS Updates

**If an EAS Update causes issues:**
```bash
npm run ci-cd:local:rollback
```

**Rollback process:**
1. Reverts to the previous stable EAS Update
2. Users automatically get the rollback on next app launch
3. No manual intervention required from users

## Manual Backend Deployment

### When to Use Manual Deployment

**Use manual backend deployment for:**
- ✅ **Quick backend fixes** - Deploy specific functions only
- ✅ **Testing changes** - Deploy to dev environment for testing
- ✅ **Hotfixes** - Urgent production fixes without full deployment
- ✅ **Content updates** - Static content or configuration changes

### Backend Deployment Commands

**Deploy all backend services:**
```bash
# Deploy everything to development
npm run deploy:local:dev

# Deploy everything to production
npm run deploy:local:prod
```

**Deploy specific components:**
```bash
# Deploy only Firebase Functions (you must edit package.json first)
npm run deploy:local:dev:only     # Development
npm run deploy:local:prod:only    # Production

# Deploy only static content/hosting
npm run deploy:ci:dev:content     # Development
npm run deploy:ci:prod:content    # Production
```

> [!IMPORTANT]
> **Function-specific deployment**: To deploy a specific function, you must first edit the function name in `package.json` under the respective deploy script.

### Static Content Deployment

**Static content includes:**
- Wellness blog posts and quotes
- App configuration files
- Web sign-up portal updates
- Link redirect page changes

**Deploy static content only:**
```bash
npm run deploy:ci:prod:content    # Production static content
npm run deploy:ci:dev:content     # Development static content
```

## Rollback Procedures

### Backend Rollback

**For backend issues:**
1. **Identify the last stable commit:**
   ```bash
   git log --oneline    # Find the last working commit
   ```

2. **Checkout the stable commit:**
   ```bash
   git checkout <stable-commit-hash>
   ```

3. **Deploy the stable version:**
   ```bash
   npm run deploy:local:prod
   ```

### Mobile App Rollback

**For mobile app issues:**
> [!WARNING]
> **No app version rollback**: You cannot downgrade user app versions. The only solution is to release a new version with fixes.

**App rollback strategy:**
1. **Fix the issue** in a new commit
2. **Create a new build** with the fix
3. **Submit to app stores** for expedited review
4. **Use EAS Update** for JavaScript-only fixes (if applicable)


## App Store Management

### App Store Dashboards

**Monitor and manage app store submissions:**
- [**Apple App Store Connect**](https://appstoreconnect.apple.com/apps/**********/appstore/ios/version/inflight) - iOS app management
- [**Google Play Console**](https://play.google.com/console/u/0/developers/5892814659496571933/app/4973741014673037397/app-dashboard) - Android app management

**Public app store listings:**
- [**FlyFit on Apple App Store**](https://apps.apple.com/us/app/fly-fit-fitness/id**********) - iOS download page
- [**FlyFit on Google Play**](https://play.google.com/store/apps/details?id=com.flybodies.flyfit&hl=en&gl=US) - Android download page

### App Store Submission Process

**When builds are submitted to app stores:**
1. **Automatic submission** - Happens when you select "deploy, release, and submit builds" in CI/CD script
2. **Manual submission** - Upload builds through respective app store consoles
3. **Review process** - Apple: 1-3 days, Google: 1-7 days
4. **Release** - Apps go live after approval

### App Store Screenshots

**Screenshot requirements for app store submissions:**

| Platform | Device Type | Emulator/Device | OS Version |
|----------|-------------|-----------------|------------|
| 🍎 **Apple App Store** | iPhone | iPhone 16 Plus (6.9") | iOS 18 |
| 🍎 **Apple App Store** | iPad | iPad Pro 13-inch (M4) | iOS 18 |
| 🤖 **Google Play Store** | Android Phone | Google Pixel 4 XL | Android 16 (API 36) |

**Screenshot creation process:**
1. **Take screenshots** on the specified devices/emulators
2. **Create mockups** using [App Mockup Studio](https://studio.app-mockup.com/) (free)
3. **Load existing templates** from `assets/screenshots/*.mockup` files for EACH device type *separately*
  - i.e., `assets/screenshots/FlyFit v3 iPhone.mockup` for iPhone screenshots
4. **Export final images** in required app store formats

**Mockup device templates:**
- Apple iPhone 16 Pro Max
- Apple iPad Pro 12.9 Inch
- Google Pixel 4 XL

> [!TIP]
> **Reuse existing mockups**: Load the `.mockup` files from `assets/screenshots/` to maintain consistent branding and layout.

## Release Management

### Release Notes

**For every FlyFit release**, create release notes for internal stakeholders:

**Distribution:**
- Share in [#proj-fly-fit-app Slack channel](https://flybodies.slack.com/archives/C06QMR0KDDM)
- Focus on **user-facing changes**, not technical details
- Use **descriptive language** for feature impact

**Release note categories:**
- ✨ **New Features** - Major new functionality
- 🐞 **Bug Fixes** - Issues resolved
- 🎨 **UI/Design Updates** - Visual improvements
- 🚀 **Performance Improvements** - Speed/efficiency gains
- 🛠️ **Improvements** - Other enhancements and fixes

**Example release note:**
```
v1.20.19 released to TestFlight with

- ✨ Added challenge public invite link to join
  - trainer can copy the challenge's public invite link and share it and create QR code from the link
  - when users click on the link, it will redirect them to the invite code modal
  - the invite code modal now will allow public invites, where users can create their own team and become team captain, or join a public team automatically created and managed for them
- ✨ Added ability for users to leave a challenge
- 🐞 Fix a background auth error that was happening since the last update switched to loading the app with cached auth
```

### Version Management

**Version numbering strategy:**
- **Major versions** (2.0.0) - Marketing release only (rarely, never done)
- **Minor versions** (2.1.0) - Native dependency updates or features added
- **Patch versions** (2.1.1) - All JavaScript fixes, large and small

**How commit types relate to versions:**
- `feat` commits → Minor version bump (2.1.0)
- `fix` commits → Patch version bump (2.1.1)
- Other commit types → Usually patch version bump

> [!NOTE]
> **Commit message guidelines**: See the [Commit Messages section](./CONTRIBUTING.md#commit-messages) in the Contributing Guide for detailed commit formatting standards that help determine these version bumps.

**Release frequency:**
- **Production releases** - Weekly to bi-weekly
- **Hotfixes** - As needed for critical issues
- **EAS Updates** - Can be very often, although have not used this much

## Next Steps

### ✅ Deployment Ready

**You should now be able to:**
- [ ] Deploy backend services to dev and production
- [ ] Create mobile app builds via EAS
- [ ] Push JavaScript-only updates
- [ ] Submit apps to app stores
- [ ] Handle rollbacks and emergency fixes

### 🚀 Continue Learning

**Related documentation:**
- [**Operations Guide**](./OPERATIONS.md) - Project links and monitoring resources
- [**Contributing Guidelines**](./CONTRIBUTING.md) - Development workflow after deployment
- [**Architecture Guide**](./ARCHITECTURE.md) - Understanding what you're deploying
